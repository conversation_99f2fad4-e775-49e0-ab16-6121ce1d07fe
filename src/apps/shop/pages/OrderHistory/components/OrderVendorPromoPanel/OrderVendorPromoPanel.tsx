import { CollapsiblePanel } from '@/libs/ui/CollapsiblePanel/CollapsiblePanel';
import { Button, Divider, Image, Text } from '@mantine/core';
import { Flex } from '@/libs/ui/Flex/Flex';
import defaultProductImgUrl from '@/assets/images/default-product.png';
import CartIcon from '@/assets/images/cart/cart-summary.svg?react';
import PlusIcon from '@/assets/images/plus.svg?react';
import { OrderStatus } from '../OrderStatus/OrderStatus';
import { getPriceString } from '@/utils';
import { FEATURE_FLAGS } from '@/constants';
import { getPromotionTypeDisplayName } from '../../utils/processOrderPromotions';
import type {
  OrderHistoryDetailVendorOrderType,
  OrderHistoryPromotionData,
} from '@/libs/orders/types';
import { ProductCartHorizontal } from '@/libs/products/components/ProductCardHorizontal/ProductCartHorizontal';
import { getProductUrl } from '@/apps/shop/routes/utils';
import { Link } from 'react-router-dom';
import { useCartStore } from '@/apps/shop/stores/useCartStore/useCartStore';

interface OrderVendorPromoPanelProps {
  vendor: OrderHistoryDetailVendorOrderType['vendor'];
  promotionData: OrderHistoryPromotionData;
  totalPrice: number;
  totalItems: number;
}

export const OrderVendorPromoPanel = ({
  vendor,
  promotionData,
}: OrderVendorPromoPanelProps) => {
  const { addToCart } = useCartStore();

  const handleAddPromotionToCart = () => {
    if (!promotionData.items || promotionData.items.length === 0) return;

    // For buy_x_get_y promotions, add only the triggering items (paid items)
    // For rebate promotions, add all items since there are no free items
    const offers = promotionData.items
      .map((item) => ({
        productOfferId: item.productOfferId,
        quantity:
          promotionData.promotion.type === 'buy_x_get_y'
            ? item.quantity - (promotionData.freeItemsQty || 0) // Only paid quantity for buy_x_get_y
            : item.quantity, // Full quantity for rebate promotions
      }))
      .filter((offer) => offer.quantity > 0);

    if (offers.length === 0) return;

    addToCart({
      offers,
      onError: (message: string) => {
        console.error('Failed to add promotion to cart:', message);
      },
    });
  };

  if (!promotionData || !promotionData.promotion) return null;

  return (
    <CollapsiblePanel
      startOpen
      header={
        <Flex align="center" pr="5rem">
          <Image
            src={vendor.imageUrl}
            alt={vendor.name}
            fallbackSrc={defaultProductImgUrl}
            h={42}
          />
          <div className="ml-4">
            <div className="mb-1">
              <span className="font-medium text-green-700">
                Promotion •{' '}
                {getPromotionTypeDisplayName(promotionData.promotion.type)}
              </span>
            </div>
            <Text fw="500" c="#333" size="md">
              {promotionData.promotion.name}
            </Text>
            <Text c="#666" size="xs" mt="0.2rem">
              {promotionData.promotion.type === 'rebate' ? (
                <>
                  Subtotal: {getPriceString(promotionData.subtotalPaidItems)}
                  {promotionData.rebatePercentage &&
                    promotionData.rebateAmount && (
                      <>
                        {' '}
                        • Rebate ({promotionData.rebatePercentage}%):{' '}
                        {getPriceString(promotionData.rebateAmount)}
                      </>
                    )}
                </>
              ) : (
                <>
                  Total: {getPriceString(promotionData.subtotalAllItems)} • Net
                  Total: {getPriceString(promotionData.subtotalPaidItems)}
                </>
              )}
            </Text>
          </div>
        </Flex>
      }
      content={
        <Flex p="md" direction="column">
          {/* Summary row with cost breakdown */}
          <div className="mb-4 rounded-lg bg-[#F8FBFD] p-4">
            <Flex justify="space-between" align="center">
              <div>
                {promotionData.promotion.type === 'rebate' ? (
                  <Text size="sm" c="#666">
                    Rebate promotion on{' '}
                    <Text span fw="500" c="#333">
                      {promotionData.paidItemsQty}
                    </Text>{' '}
                    {promotionData.paidItemsQty === 1 ? 'product' : 'products'}
                  </Text>
                ) : (
                  <Text size="sm" c="#666">
                    You got total of{' '}
                    <Text span fw="500" c="#333">
                      {promotionData.paidItemsQty + promotionData.freeItemsQty}
                    </Text>{' '}
                    products ({promotionData.paidItemsQty} paid +{' '}
                    {promotionData.freeItemsQty} free)
                  </Text>
                )}
              </div>
              <div className="text-right">
                <Text size="sm" c="#666">
                  {promotionData.promotion.type === 'rebate' ? (
                    <>
                      Rebate Savings:{' '}
                      <Text span fw="500" c="#333">
                        {getPriceString(promotionData.rebateAmount || 0)}
                      </Text>
                    </>
                  ) : (
                    <>
                      Promotional Savings:{' '}
                      <Text span fw="500" c="#333">
                        {getPriceString(
                          promotionData.subtotalAllItems -
                            promotionData.subtotalPaidItems,
                        )}
                      </Text>
                    </>
                  )}
                </Text>
              </div>
            </Flex>
            {FEATURE_FLAGS.ORDER_STORY_COMPLETE && (
              <Flex justify="flex-end" mt="md">
                <Button onClick={handleAddPromotionToCart} size="sm">
                  <CartIcon />
                  <PlusIcon />
                  <Text ml="xs">Add to Cart</Text>
                </Button>
              </Flex>
            )}
          </div>

          {/* Individual promotion items */}
          {promotionData.items.map(
            (
              {
                id,
                unitPrice,
                totalPrice,
                quantity,
                orderNumber,
                status,
                product,
                productOfferId,
              },
              index,
            ) => {
              const isRebatePromotion =
                promotionData.promotion.type === 'rebate';
              const freeItemsQty = isRebatePromotion
                ? 0
                : promotionData.freeItemsQty || 0;
              const paidQuantity = isRebatePromotion
                ? quantity
                : quantity - freeItemsQty;

              return (
                <div key={id}>
                  {index !== 0 ? <Divider my="md" /> : null}
                  <ProductCartHorizontal
                    product={product}
                    productOfferId={productOfferId}
                    content={
                      <Flex>
                        <div className="ml-4">
                          <Text
                            fw="500"
                            size="xs"
                            mb="0.1rem"
                            c="rgba(102, 102, 102, 0.70)"
                          >
                            Order ID: {orderNumber}
                          </Text>
                          <Link
                            to={getProductUrl(product.id, productOfferId)}
                            className="no-underline hover:underline"
                          >
                            <Text fw="500" c="#333">
                              {product.name}
                            </Text>
                          </Link>
                          <Flex mt="lg" direction="column" gap="xs">
                            {isRebatePromotion ? (
                              /* Rebate promotion - show single row with rebate info */
                              <Flex>
                                <Text
                                  py="sm"
                                  pr="sm"
                                  miw="6rem"
                                  size="xs"
                                  c="#666"
                                >
                                  Quantity:{' '}
                                  <Text c="#333" fw="700" span>
                                    {quantity}
                                  </Text>
                                </Text>
                                <Divider orientation="vertical" />
                                <Text p="sm" size="xs" c="#666">
                                  Price:{' '}
                                  <Text c="#333" fw="700" span>
                                    {getPriceString(unitPrice)}
                                  </Text>
                                </Text>
                                <Divider orientation="vertical" />
                                <Text p="sm" size="xs" c="#666">
                                  Net Total:{' '}
                                  <Text c="#333" fw="700" span>
                                    {getPriceString(totalPrice)}
                                  </Text>
                                </Text>
                                {promotionData.rebatePercentage && (
                                  <>
                                    <Divider orientation="vertical" />
                                    <Text p="sm" size="xs" c="#666">
                                      Rebate:{' '}
                                      <Text c="green" fw="700" span>
                                        {promotionData.rebatePercentage}%
                                      </Text>
                                    </Text>
                                  </>
                                )}
                              </Flex>
                            ) : (
                              /* Buy X Get Y promotion - show paid and free rows */
                              <>
                                {/* Paid items row */}
                                {paidQuantity > 0 && (
                                  <Flex>
                                    <Text
                                      py="sm"
                                      pr="sm"
                                      miw="6rem"
                                      size="xs"
                                      c="#666"
                                    >
                                      Paid Qty:{' '}
                                      <Text c="#333" fw="700" span>
                                        {paidQuantity}
                                      </Text>
                                    </Text>
                                    <Divider orientation="vertical" />
                                    <Text p="sm" size="xs" c="#666">
                                      Price:{' '}
                                      <Text c="#333" fw="700" span>
                                        {getPriceString(unitPrice)}
                                      </Text>
                                    </Text>
                                    <Divider orientation="vertical" />
                                    <Text p="sm" size="xs" c="#666">
                                      Subtotal:{' '}
                                      <Text c="#333" fw="700" span>
                                        {getPriceString(
                                          Number(unitPrice) * paidQuantity,
                                        )}
                                      </Text>
                                    </Text>
                                  </Flex>
                                )}
                                {/* Free items row */}
                                {freeItemsQty > 0 && (
                                  <Flex>
                                    <Text
                                      py="sm"
                                      pr="sm"
                                      miw="6rem"
                                      size="xs"
                                      c="#666"
                                    >
                                      Free Qty:{' '}
                                      <Text c="#333" fw="700" span>
                                        {freeItemsQty}
                                      </Text>
                                    </Text>
                                    <Divider orientation="vertical" />
                                    <Text p="sm" size="xs" c="#666">
                                      Price:{' '}
                                      <Text c="green" fw="700" span>
                                        Free
                                      </Text>
                                    </Text>
                                    <Divider orientation="vertical" />
                                    <Text p="sm" size="xs" c="#666">
                                      Product:{' '}
                                      <Text c="#333" fw="700" span>
                                        {promotionData.freeOffer?.name ||
                                          product.name}
                                      </Text>
                                    </Text>
                                  </Flex>
                                )}
                              </>
                            )}
                          </Flex>
                        </div>
                      </Flex>
                    }
                    actions={
                      <div className="text-right">
                        <Text
                          fw="500"
                          size="xs"
                          mb="0.1rem"
                          c="rgba(102, 102, 102, 0.70)"
                        >
                          Status
                        </Text>
                        <OrderStatus status={status} align="right" />
                      </div>
                    }
                  />
                </div>
              );
            },
          )}
        </Flex>
      }
    />
  );
};
