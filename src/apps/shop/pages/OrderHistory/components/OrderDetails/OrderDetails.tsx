import { Button, Divider, Text } from '@mantine/core';
import { Flex } from '@/libs/ui/Flex/Flex';

import { useOrderDetails } from '../../services/useOrderDetails';
import { getPriceString } from '@/utils';
import styles from './OrderDetails.module.css';
import { FEATURE_FLAGS } from '@/constants';
import { OrderVendorPanel } from '../OrderVendorPanel/OrderVendorPanel';
import { ContentLoader } from '@/libs/ui/ContentLoader/ContentLoader';
import { DownloadInvoicesLink } from '@/libs/orders/components/DownloadInvoicesLink/DownloadInvoicesLink';
import { DownloadChecklist } from '@/libs/orders/components/DownloadChecklist/DownloadChecklist';

interface OrderDetailsProps {
  id: string;
}
export const OrderDetails = ({ id }: OrderDetailsProps) => {
  const { order, isLoading } = useOrderDetails({ id });
  const {
    orderNumber,
    totalPrice,
    downloadInvoicesUrl,
    downloadChecklistUrl,
    vendorOrders = [],
    promotions = [],
  } = order ?? {};

  if (isLoading || !order) {
    return <ContentLoader />;
  }

  return (
    <Flex p="1.5rem" direction="column" className={styles.container}>
      <Flex gap="xl">
        <div>
          <div className="pb-4">
            <Text c="#666" size="xs" mb="xs">
              Order ID
            </Text>
            <Text c="#333" size="xlLg" fw="bold" inline>
              {orderNumber}
            </Text>
          </div>
          <Divider mb="md" />
          <div className="pb-4">
            <Text c="#666" size="xs">
              Order Total
            </Text>
            <Text c="#333" size="xlLg" fw="bold">
              {getPriceString(totalPrice)}
            </Text>
          </div>
          <div className="pb-4">
            <Divider mb="md" />
            <Flex direction="column">
              {downloadInvoicesUrl && (
                <DownloadInvoicesLink url={downloadInvoicesUrl} />
              )}
              {downloadChecklistUrl && (
                <DownloadChecklist url={downloadChecklistUrl} />
              )}
            </Flex>
          </div>
          {FEATURE_FLAGS.ORDER_STORY_COMPLETE && (
            <>
              <Flex direction="column">
                <Button>Repeat Order</Button>
              </Flex>
            </>
          )}
        </div>
        <div className="flex-grow">
          <Text c="#666" size="xs" mb="xs">
            Order Details
          </Text>
          <div className={`bg-[#F8FBFD] p-6 ${styles.info}`}>
            {vendorOrders.map(
              // TODO: Sync with BE missing fields
              (
                { items, vendor, totalTaxFee, shippingFee, totalPrice },
                index,
              ) => (
                <div key={vendor.id}>
                  {index !== 0 ? <Divider my="md" /> : null}
                  <Flex>
                    <div className="flex-1">
                      <Text c="#666" fw="400">
                        Vendor
                      </Text>
                      <Text c="#333" fw="500">
                        {vendor.name}
                      </Text>
                    </div>
                    <Divider mx="md" orientation="vertical" />
                    <div className="flex-1">
                      <Text c="#666" fw="400">
                        Line items
                      </Text>
                      <Text c="#333" fw="500">
                        {items.length}
                      </Text>
                    </div>
                    <Divider mx="md" orientation="vertical" />
                    <div className="flex-1">
                      <Text c="#666" fw="400">
                        Taxes
                      </Text>
                      <Text c="#333" fw="500">
                        {totalTaxFee ? getPriceString(totalTaxFee) : '–'}
                      </Text>
                    </div>
                    <Divider mx="md" orientation="vertical" />
                    <div className="flex-1">
                      <Text c="#666" fw="400">
                        Shipping
                      </Text>
                      <Text c="#333" fw="500">
                        {shippingFee ? getPriceString(shippingFee) : '–'}
                      </Text>
                    </div>
                    <Divider mx="md" orientation="vertical" />
                    <div className="flex-1">
                      <Text c="#666" fw="400">
                        Vendor total
                      </Text>
                      <Text c="#333" fw="500">
                        {getPriceString(totalPrice)}
                      </Text>
                    </div>
                  </Flex>
                </div>
              ),
            )}
          </div>
        </div>
      </Flex>
      <Divider my="xl" />
      {/* {vendorOrdersWithPromotions.map(
        ({ vendor, items, totalPrice, promotionData }) => (
          <div key={vendor.id} className="mb-4">
            <OrderVendorPanel
              totalPrice={+totalPrice}
              totalItems={items.length}
              items={items}
              vendor={vendor}
              promotionData={promotionData}
            />
          </div>
        ),
      )} */}
    </Flex>
  );
};
