import type { PromoType } from '@/types/common';
import type { OfferType } from '@/types';

export type ShippingStatusType =
  | 'PENDING'
  | 'PLACEMENT_FAILED'
  | 'ACCEPTED'
  | 'BACKORDERED'
  | 'SHIPPED'
  | 'REJECTED'
  | 'RETURNED'
  | 'DELIVERED'
  | 'CANCELLED'
  | 'PROCESSING'
  | 'PARTIALLY_SHIPPED'
  | 'PARTIALLY_DELIVERED';

export interface OrderHistoryItemType {
  date: string;
  id: string;
  itemsCount: number;
  orderNumber: string;
  status: ShippingStatusType;
  totalPrice: string;
  vendorsCount: number;
}

export interface OrderHistoryDetailItemType {
  id: string;
  product: {
    id: string;
    name: string;
    imageUrl: string;
  };
  productOfferId: string;
  quantity: number;
  status: ShippingStatusType;
  totalPrice: string;
  unitPrice: string;
  taxFee: string | null;
  orderNumber: string;
  // Promotion-related fields
  promotion?: PromoType | null;
  freeItemsQty?: number;
  freeOffer?: OfferType | null;
  isPromotionItem?: boolean;
}

// API response promotion structure
export interface OrderHistoryPromotionTriggeringItem {
  productOfferId: string;
  productId: string;
  quantity: number;
}

export interface OrderHistoryPromotionRuleCondition {
  id: string;
  type: string;
  description: string;
  config: Record<string, any>;
}

export interface OrderHistoryPromotionRuleAction {
  id: string;
  type: string;
  description: string;
  config: Record<string, any>;
}

export interface OrderHistoryPromotionAppliedRule {
  ruleId: string;
  priority: number;
  conditions: OrderHistoryPromotionRuleCondition[];
  actions: OrderHistoryPromotionRuleAction[];
}

export interface OrderHistoryPromotionAppliedBenefit {
  type: 'rebate' | 'give_free_product';
  percentage?: number;
  quantity?: string;
  productOfferId?: string | null;
  description?: string;
  message?: string | null;
}

export interface OrderHistoryPromotion {
  id: string;
  name: string;
  type: 'rebate' | 'buy_x_get_y';
  triggeringItems: OrderHistoryPromotionTriggeringItem[];
  appliedRules: OrderHistoryPromotionAppliedRule[];
  appliedBenefits: OrderHistoryPromotionAppliedBenefit[];
}

// Processed promotion data for UI display
export interface OrderHistoryPromotionData {
  subtotalPaidItems: number;
  subtotalAllItems: number;
  paidItemsQty: number;
  freeItemsQty: number;
  freeOffer: OfferType | null;
  promotion: OrderHistoryPromotion;
  items: OrderHistoryDetailItemType[];
  imageUrl: string;
  manufacturer: string;
  rebatePercentage?: number;
  rebateAmount?: number;
}

export interface OrderHistoryDetailVendorOrderType {
  id: string;
  items: OrderHistoryDetailItemType[];
  totalPrice: string;
  totalTaxFee: string | null;
  shippingFee: string | null;
  vendor: {
    id: string;
    name: string;
    imageUrl: string;
  };
  // Processed promotion data for this vendor (computed from order-level promotions)
  promotionData?: OrderHistoryPromotionData | null;
}

export interface OrderHistoryDetailsType {
  date: string;
  id: string;
  vendorOrders: OrderHistoryDetailVendorOrderType[];
  orderNumber: string;
  status: ShippingStatusType;
  totalPrice: string;
  downloadChecklistUrl: string;
  downloadInvoicesUrl: string | null;
  // Promotions from API response (order level)
  promotions?: OrderHistoryPromotion[];
}
